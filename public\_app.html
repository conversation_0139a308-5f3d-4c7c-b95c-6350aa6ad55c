<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Econic Media App Diagnostic</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        line-height: 1.6;
        color: #333;
      }
      h1 { color: #2563eb; }
      pre { 
        background: #f1f1f1; 
        padding: 1rem;
        border-radius: 4px;
        overflow-x: auto;
      }
    </style>
  </head>
  <body>
    <h1>Econic Media Deployment Diagnostic</h1>
    <p>If you're seeing this page, it means the Vercel deployment is working at a basic level, but the React app isn't loading properly.</p>
    <div id="debug-info"></div>
    
    <script>
      // Basic diagnostic information
      const debugInfo = document.getElementById('debug-info');
      
      const info = {
        url: window.location.href,
        pathname: window.location.pathname,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        windowSize: `${window.innerWidth}x${window.innerHeight}`,
      };
      
      // Create HTML output
      debugInfo.innerHTML = `
        <h2>Diagnostic Information</h2>
        <pre>${JSON.stringify(info, null, 2)}</pre>
        <p>Please try refreshing the page or visiting the <a href="/">home page</a>.</p>
      `;
    </script>
  </body>
</html>
