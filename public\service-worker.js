// Service Worker for Econic Media Website
// Improves performance through caching and offline capabilities
// Enhanced with better error handling and frame management

const CACHE_NAME = 'econic-media-cache-v6';

// Check if we're in development mode
const isDevelopment = self.location.hostname === 'localhost' || self.location.hostname === '127.0.0.1';

// Enhanced error handling function with comprehensive extension filtering
const handleServiceWorkerError = (error, context = 'general') => {
  const errorMessage = error?.message || error?.toString() || '';

  // Comprehensive filter for browser extension and frame-related errors
  const extensionPatterns = [
    'Frame with ID', 'No tab with id', 'message port closed',
    'Extension context', 'background.js', 'contentscript.bundle.js',
    'The message port closed before a response was received',
    'runtime.lastError', 'Extension context invalidated',
    'Cannot access contents of', 'Script error',
    'chrome-extension://', 'moz-extension://', 'safari-extension://',
    'edge-extension://', 'Uncaught (in promise)',
    'Non-Error promise rejection captured'
  ];

  // Check if error is extension-related
  if (extensionPatterns.some(pattern => errorMessage.includes(pattern))) {
    return; // Silently ignore extension-related errors
  }

  // Only log actual service worker errors in production
  if (!isDevelopment) {
    console.error(`Service Worker ${context} error:`, error);
  }
};

// Skip service worker functionality in development to avoid console errors
if (isDevelopment) {
  console.log('Service Worker: Development mode detected, minimal functionality enabled');
}
// Core assets that are essential for the site to function
const CORE_ASSETS = [
  '/',
  '/index.html',
  '/newlogofinal.png', // Site logo
];

// Critical images that should be cached (first 4 product images for above-the-fold content)
// Using only URL-encoded versions to prevent duplicate caching
const CRITICAL_IMAGES = [
  '/Product%20Pictures/1%20(1).png',
  '/Product%20Pictures/1%20(2).png',
  '/Product%20Pictures/1%20(3).png',
  '/Product%20Pictures/1%20(4).png',
];

// Combine core assets and critical images for initial cache
const STATIC_ASSETS = [...CORE_ASSETS, ...CRITICAL_IMAGES];

// Installation event - cache core assets with graceful error handling
self.addEventListener('install', (event) => {
  // In development, skip caching to avoid errors
  if (isDevelopment) {
    console.log('Service Worker: Install event - skipping cache in development');
    self.skipWaiting();
    return;
  }

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(async (cache) => {
        // Cache assets individually to handle missing files gracefully
        const cachePromises = STATIC_ASSETS.map(async (asset) => {
          try {
            const response = await fetch(asset);
            if (response.ok) {
              await cache.put(asset, response);
            } else {
              console.warn(`Service Worker: Failed to cache ${asset} - ${response.status}`);
            }
          } catch (error) {
            console.warn(`Service Worker: Error caching ${asset}:`, error.message);
          }
        });

        await Promise.allSettled(cachePromises);
        return self.skipWaiting();
      })
      .catch((error) => {
        handleServiceWorkerError(error, 'installation');
        // Continue with installation even if caching fails
        self.skipWaiting();
      })
  );
});

// Activation event - clean up old caches
self.addEventListener('activate', (event) => {
  // In development, skip cache cleanup
  if (isDevelopment) {
    console.log('Service Worker: Activate event - skipping cache cleanup in development');
    self.clients.claim();
    return;
  }

  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((name) => {
          if (name !== CACHE_NAME) {
            if (!isDevelopment) {
              console.log('Deleting old cache:', name);
            }
            return caches.delete(name);
          }
        })
      );
    }).then(() => {
      if (!isDevelopment) {
        console.log('Service worker activated and old caches cleaned');
      }
      return self.clients.claim();
    }).catch((error) => {
      handleServiceWorkerError(error, 'activation');
    })
  );
});

// Network first with cache fallback strategy for images
const imageStrategy = async (request) => {
  try {
    // Try network first
    const networkResponse = await fetch(request);

    // Cache the response if it's valid
    if (networkResponse && networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // If network fails, try cache with both encoded and decoded URLs
    let cachedResponse = await caches.match(request);

    if (!cachedResponse) {
      // Try with URL decoded version
      const decodedUrl = decodeURIComponent(request.url);
      const decodedRequest = new Request(decodedUrl, request);
      cachedResponse = await caches.match(decodedRequest);
    }

    if (!cachedResponse) {
      // Try with URL encoded version
      const encodedUrl = encodeURI(request.url);
      const encodedRequest = new Request(encodedUrl, request);
      cachedResponse = await caches.match(encodedRequest);
    }

    // Filter out extension-related errors before logging
    const errorMessage = error?.message || '';
    const extensionPatterns = ['Frame with ID', 'No tab with id', 'message port closed', 'Extension context'];

    if (!extensionPatterns.some(pattern => errorMessage.includes(pattern))) {
      console.warn('Image loading failed:', request.url, error);
    }

    return cachedResponse || Promise.reject('Not found in cache');
  }
};

// Cache first with network fallback for static assets
const cacheFirstStrategy = async (request) => {
  // Try the cache first
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    // If not found in cache, fetch from network and cache it
    const networkResponse = await fetch(request);

    if (networkResponse && networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // If both cache and network fail
    return Promise.reject('Resource not available');
  }
};

// Fetch event - intercept requests and apply caching strategies
self.addEventListener('fetch', (event) => {
  // In development, skip fetch interception to avoid errors
  if (isDevelopment) {
    return;
  }

  // Skip cross-origin requests to avoid CORS issues with Vercel
  const url = new URL(event.request.url);
  if (url.origin !== self.location.origin) {
    return;
  }

  // Don't cache API calls
  if (url.pathname.startsWith('/api/')) {
    return;
  }

  try {
    // Apply image strategy for image files
    if (event.request.destination === 'image' || url.pathname.match(/\.(png|jpg|jpeg|gif|webp|svg|ico)$/i)) {
      event.respondWith(imageStrategy(event.request));
      return;
    }

    // Apply cache-first strategy for static assets
    if (event.request.destination === 'style' ||
        event.request.destination === 'script' ||
        event.request.destination === 'font' ||
        url.pathname.match(/\.(css|js|woff2?)$/i)) {
      event.respondWith(cacheFirstStrategy(event.request));
      return;
    }

    // Network-first for HTML documents
    if (event.request.mode === 'navigate' || event.request.destination === 'document') {
      event.respondWith(
        fetch(event.request).catch(() => {
          return caches.match(event.request);
        })
      );
      return;
    }
  } catch (error) {
    handleServiceWorkerError(error, 'fetch');
  }
});

// Add global error handler for unhandled service worker errors
self.addEventListener('error', (event) => {
  handleServiceWorkerError(event.error, 'global');
});

// Add unhandled promise rejection handler
self.addEventListener('unhandledrejection', (event) => {
  handleServiceWorkerError(event.reason, 'promise rejection');
  event.preventDefault(); // Prevent the error from appearing in console
});
