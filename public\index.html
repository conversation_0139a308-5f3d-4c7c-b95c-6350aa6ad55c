<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Econic Media | Web Design & Product Photography</title>
    <meta name="description" content="Econic Media - Modern web design and professional product photography for ambitious brands" />
    <meta name="author" content="Econic Media" />

    <meta property="og:title" content="Econic Media | Web Design & Product Photography" />
    <meta property="og:description" content="Modern web design and professional product photography for ambitious brands" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@EconicMedia" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    
    <link rel="icon" href="/newlogofinal.png" type="image/png" />
    
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        line-height: 1.6;
        color: #ffffff;
        background-color: #0f1116;
        margin: 0;
        padding: 0;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
      }
      header {
        padding: 4rem 0;
      }
      h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #4da7ff;
      }
      .subtitle {
        font-size: 1.5rem;
        margin-bottom: 3rem;
        color: #a1c4fd;
      }
      .cta-button {
        display: inline-block;
        background: linear-gradient(45deg, #4da7ff, #a1c4fd);
        color: white;
        padding: 0.8rem 2rem;
        border-radius: 30px;
        text-decoration: none;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(77, 167, 255, 0.4);
        transition: all 0.3s ease;
      }
      .cta-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(77, 167, 255, 0.6);
      }
      footer {
        margin-top: 5rem;
        padding: 2rem 0;
        color: #66768f;
      }
      .status-message {
        padding: 1rem;
        background-color: rgba(30, 41, 59, 0.6);
        border-radius: 8px;
        margin: 2rem 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <header>
        <h1>Econic Media</h1>
        <p class="subtitle">Modern web design & professional product photography</p>
        
        <div class="status-message">
          <p>Our website is currently being updated with exciting new features and performance improvements!</p>
          <p>Please check back soon or contact us directly for immediate assistance.</p>
        </div>
        
        <a href="mailto:<EMAIL>" class="cta-button">Contact Us</a>
      </header>
      
      <main>
        <div style="margin: 4rem 0;">
          <h2>Services We Offer</h2>
          <ul style="list-style: none; padding: 0; display: flex; justify-content: center; flex-wrap: wrap; gap: 2rem;">
            <li style="flex: 1; min-width: 250px; background-color: rgba(30, 41, 59, 0.6); padding: 2rem; border-radius: 8px;">
              <h3>Web Design</h3>
              <p>Modern, responsive websites that convert visitors into customers</p>
            </li>
            <li style="flex: 1; min-width: 250px; background-color: rgba(30, 41, 59, 0.6); padding: 2rem; border-radius: 8px;">
              <h3>Product Photography</h3>
              <p>Professional product photos that showcase your products in the best light</p>
            </li>
            <li style="flex: 1; min-width: 250px; background-color: rgba(30, 41, 59, 0.6); padding: 2rem; border-radius: 8px;">
              <h3>Brand Strategy</h3>
              <p>Comprehensive brand strategy to help your business stand out from the competition</p>
            </li>
          </ul>
        </div>
      </main>
      
      <footer>
        <p>&copy; 2025 Econic Media. All rights reserved.</p>
      </footer>
    </div>

    <!-- Script to redirect to React app once it's fixed -->
    <script>
      // Once the React app is working, uncomment this line and update the path
      // window.location.href = "/app";
    </script>
  </body>
</html>
