import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { LanguageProvider } from '@/context/LanguageContext';
import Navbar from '../Navbar';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    nav: ({ children, ...props }: any) => <nav {...props}>{children}</nav>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <LanguageProvider>
        {component}
      </LanguageProvider>
    </BrowserRouter>
  );
};

describe('Navbar', () => {
  it('renders without crashing', () => {
    renderWithProviders(<Navbar />);
  });

  it('displays the logo', () => {
    renderWithProviders(<Navbar />);
    
    // Check for logo or brand name
    const logo = screen.getByRole('link', { name: /econic media/i });
    expect(logo).toBeInTheDocument();
  });

  it('has navigation links', () => {
    renderWithProviders(<Navbar />);
    
    // Check for navigation links
    const navLinks = screen.getAllByRole('link');
    expect(navLinks.length).toBeGreaterThan(1);
  });

  it('has proper accessibility attributes', () => {
    renderWithProviders(<Navbar />);
    
    // Check for navigation landmark
    const nav = screen.getByRole('navigation');
    expect(nav).toBeInTheDocument();
  });

  it('handles mobile menu toggle', () => {
    renderWithProviders(<Navbar />);
    
    // Look for mobile menu button (usually a hamburger menu)
    const mobileMenuButton = screen.queryByRole('button', { name: /menu/i });
    
    if (mobileMenuButton) {
      fireEvent.click(mobileMenuButton);
      // Add assertions for mobile menu behavior
    }
  });
});
