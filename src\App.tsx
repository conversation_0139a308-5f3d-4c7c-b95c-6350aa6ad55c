import React, { Suspense, lazy, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import ResourceHints from './components/ResourceHints';
import ErrorBoundary from './components/ErrorBoundary';
import usePerformanceMonitoring from './hooks/use-performance-monitoring';
import { initializePerformanceOptimizations } from './utils/performance';
import './lib/custom-properties.css';

// Conditionally import Vercel components only in production
const SpeedInsights = import.meta.env.MODE === 'production'
  ? lazy(() => import('@vercel/speed-insights/react').then(module => ({ default: module.SpeedInsights })))
  : null;

const Analytics = import.meta.env.MODE === 'production'
  ? lazy(() => import('@vercel/analytics/react').then(module => ({ default: module.Analytics })))
  : null;

// Use React.lazy for code splitting
const Index = lazy(() => import("./pages/Index"));
const NotFound = lazy(() => import("./pages/NotFound"));

// Initialize Query Client outside component to avoid recreation
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Loading fallback component
const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="w-16 h-16 border-4 border-neon-cyan/20 border-t-neon-cyan rounded-full animate-spin"></div>
  </div>
);

function App() {
  // Use performance monitoring hook
  usePerformanceMonitoring();

  // Initialize performance optimizations
  useEffect(() => {
    initializePerformanceOptimizations();
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ResourceHints />
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <ErrorBoundary>
              <Suspense fallback={<LoadingFallback />}>
                <Routes>
                  <Route path="/" element={<Index />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Suspense>
            </ErrorBoundary>
          </BrowserRouter>
          {/* Only render Vercel components in production */}
          {SpeedInsights && (
            <Suspense fallback={null}>
              <SpeedInsights />
            </Suspense>
          )}
          {Analytics && (
            <Suspense fallback={null}>
              <Analytics />
            </Suspense>
          )}
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
