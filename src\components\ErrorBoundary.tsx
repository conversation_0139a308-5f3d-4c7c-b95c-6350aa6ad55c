import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>riangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Filter out browser extension related errors
    const errorMessage = error?.message || error?.toString() || '';
    const isExtensionError = errorMessage.includes('Frame with ID') ||
                           errorMessage.includes('No tab with id') ||
                           errorMessage.includes('Extension context') ||
                           errorMessage.includes('background.js') ||
                           errorMessage.includes('chrome-extension://') ||
                           errorMessage.includes('moz-extension://') ||
                           errorMessage.includes('safari-extension://') ||
                           errorMessage.includes('edge-extension://') ||
                           errorMessage.includes('Unchecked runtime.lastError');

    // Don't trigger error boundary for extension-related errors
    if (isExtensionError) {
      return { hasError: false };
    }

    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Filter out browser extension related errors
    const errorMessage = error?.message || error?.toString() || '';
    const isExtensionError = errorMessage.includes('Frame with ID') ||
                           errorMessage.includes('No tab with id') ||
                           errorMessage.includes('Extension context') ||
                           errorMessage.includes('background.js') ||
                           errorMessage.includes('chrome-extension://') ||
                           errorMessage.includes('moz-extension://') ||
                           errorMessage.includes('safari-extension://') ||
                           errorMessage.includes('edge-extension://') ||
                           errorMessage.includes('Unchecked runtime.lastError');

    // Don't show error boundary for extension-related errors
    if (isExtensionError) {
      // Reset the error state and continue normal operation
      this.setState({ hasError: false, error: undefined, errorInfo: undefined });
      return;
    }

    // Log error to monitoring service (only for actual app errors)
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    this.setState({
      error,
      errorInfo
    });

    // In production, you would send this to an error reporting service
    if (import.meta.env.MODE === 'production') {
      // Example: Sentry.captureException(error, { extra: errorInfo });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-[400px] flex items-center justify-center p-8">
          <div className="text-center max-w-md">
            <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Something went wrong
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              We're sorry, but something unexpected happened. Please try refreshing the page.
            </p>
            <button
              type="button"
              onClick={this.handleRetry}
              className="inline-flex items-center px-4 py-2 bg-neon-cyan text-black rounded-lg hover:bg-neon-cyan/80 transition-colors"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </button>

            {import.meta.env.MODE === 'development' && this.state.error && (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                  Error Details (Development Only)
                </summary>
                <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
