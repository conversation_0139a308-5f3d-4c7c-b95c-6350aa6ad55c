import React from 'react';
import { render } from '@testing-library/react';
import OrbitingIcons from '../OrbitingIcons';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props}>{children}</div>,
  },
}));

describe('OrbitingIcons', () => {
  it('renders without crashing', () => {
    render(<OrbitingIcons />);
  });

  it('applies custom className when provided', () => {
    const { container } = render(<OrbitingIcons className="custom-class" />);
    const orbitingContainer = container.firstChild as HTMLElement;
    expect(orbitingContainer).toHaveClass('custom-class');
  });

  it('has proper accessibility attributes', () => {
    const { container } = render(<OrbitingIcons />);
    const orbitingContainer = container.firstChild as HTMLElement;

    // Should be non-interactive
    expect(orbitingContainer).toHaveClass('pointer-events-none');
    expect(orbitingContainer).toHaveAttribute('aria-hidden', 'true');
    expect(orbitingContainer).toHaveAttribute('role', 'presentation');
  });

  it('contains responsive visibility classes', () => {
    const { container } = render(<OrbitingIcons />);

    // Should have desktop and mobile versions
    const desktopElements = container.querySelectorAll('.hidden.md\\:block');
    const mobileElements = container.querySelectorAll('.block.md\\:hidden');

    expect(desktopElements.length).toBeGreaterThan(0);
    expect(mobileElements.length).toBeGreaterThan(0);
  });

  it('renders with proper overflow handling', () => {
    const { container } = render(<OrbitingIcons />);
    const orbitingContainer = container.firstChild as HTMLElement;

    expect(orbitingContainer).toHaveClass('overflow-hidden');
  });

  it('includes motion-safe and motion-reduce classes for accessibility', () => {
    const { container } = render(<OrbitingIcons />);

    // Should have motion-safe and motion-reduce classes
    const motionSafeElements = container.querySelectorAll('.motion-safe\\:block');
    const motionReduceElements = container.querySelectorAll('.motion-reduce\\:hidden');

    expect(motionSafeElements.length).toBeGreaterThan(0);
    expect(motionReduceElements.length).toBeGreaterThan(0);
  });

  it('renders orbital icons with proper structure', () => {
    const { container } = render(<OrbitingIcons />);

    // Should have orbital-icon elements
    const orbitalIcons = container.querySelectorAll('.orbital-icon');
    expect(orbitalIcons.length).toBeGreaterThan(0);
  });
});
