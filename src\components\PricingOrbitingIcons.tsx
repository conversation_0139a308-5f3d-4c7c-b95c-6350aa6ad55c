import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  DollarSign,
  Star,
  Shield,
  BarChart3,
  Headphones,
  Zap,
  Target,
  Award,
  TrendingUp,
  CheckCircle,
  Sparkles,
  Crown,
  Gem,
  Medal,
  LineChart,
  Activity,
  PieChart,
  Building2,
  Users,
  Globe,
  Settings,
  Lightbulb,
  Cog,
  Database,
  Briefcase,
  Calculator,
  FileText,
  Handshake,
  Network,
  Layers,
  Package,
  Workflow,
  Compass,
  Map,
  Bookmark
} from 'lucide-react';

// Business-relevant icons for pricing section orbital animation
// Scaled-down version of hero orbital system with pricing-specific themes

// MAXIMUM SCALE 4-Path Orbital System - Pricing-focused with maximum visual dominance
const pricingOrbitingIcons = [
  // Inner Path - Clockwise rotation (40s) - Core Pricing Elements (6 icons, 60° spacing) - MAXIMUM SCALE
  { Icon: DollarSign, color: 'text-premium-gold/70', size: 52, radius: 240, sector: 0, orbitalFamily: 'inner' },
  { Icon: Star, color: 'text-neon-cyan/65', size: 55, radius: 240, sector: 60, orbitalFamily: 'inner' },
  { Icon: Crown, color: 'text-premium-platinum/65', size: 53, radius: 240, sector: 120, orbitalFamily: 'inner' },
  { Icon: Award, color: 'text-neon-purple/65', size: 52, radius: 240, sector: 180, orbitalFamily: 'inner' },
  { Icon: Gem, color: 'text-neon-pink/65', size: 50, radius: 240, sector: 240, orbitalFamily: 'inner' },
  { Icon: Medal, color: 'text-premium-gold/65', size: 53, radius: 240, sector: 300, orbitalFamily: 'inner' },

  // Middle Path - Counterclockwise rotation (55s) - Business Growth Elements (8 icons, 45° spacing) - MAXIMUM SCALE
  { Icon: TrendingUp, color: 'text-premium-gold/60', size: 47, radius: 340, sector: 0, orbitalFamily: 'middle' },
  { Icon: BarChart3, color: 'text-neon-cyan/60', size: 49, radius: 340, sector: 45, orbitalFamily: 'middle' },
  { Icon: Target, color: 'text-neon-purple/60', size: 48, radius: 340, sector: 90, orbitalFamily: 'middle' },
  { Icon: Zap, color: 'text-premium-platinum/60', size: 47, radius: 340, sector: 135, orbitalFamily: 'middle' },
  { Icon: Shield, color: 'text-neon-pink/60', size: 48, radius: 340, sector: 180, orbitalFamily: 'middle' },
  { Icon: CheckCircle, color: 'text-premium-gold/60', size: 47, radius: 340, sector: 225, orbitalFamily: 'middle' },
  { Icon: LineChart, color: 'text-neon-cyan/60', size: 48, radius: 340, sector: 270, orbitalFamily: 'middle' },
  { Icon: Activity, color: 'text-neon-purple/60', size: 47, radius: 340, sector: 315, orbitalFamily: 'middle' },

  // Outer Path - Clockwise rotation (75s) - Professional Tools (10 icons, 36° spacing) - MAXIMUM SCALE
  { Icon: Briefcase, color: 'text-premium-platinum/55', size: 42, radius: 440, sector: 0, orbitalFamily: 'outer' },
  { Icon: PieChart, color: 'text-neon-cyan/55', size: 43, radius: 440, sector: 36, orbitalFamily: 'outer' },
  { Icon: Building2, color: 'text-premium-gold/55', size: 42, radius: 440, sector: 72, orbitalFamily: 'outer' },
  { Icon: Users, color: 'text-neon-purple/55', size: 43, radius: 440, sector: 108, orbitalFamily: 'outer' },
  { Icon: Globe, color: 'text-neon-pink/55', size: 42, radius: 440, sector: 144, orbitalFamily: 'outer' },
  { Icon: Settings, color: 'text-premium-platinum/55', size: 43, radius: 440, sector: 180, orbitalFamily: 'outer' },
  { Icon: Lightbulb, color: 'text-neon-cyan/55', size: 42, radius: 440, sector: 216, orbitalFamily: 'outer' },
  { Icon: Cog, color: 'text-premium-gold/55', size: 43, radius: 440, sector: 252, orbitalFamily: 'outer' },
  { Icon: Database, color: 'text-neon-purple/55', size: 42, radius: 440, sector: 288, orbitalFamily: 'outer' },
  { Icon: Sparkles, color: 'text-premium-platinum/55', size: 43, radius: 440, sector: 324, orbitalFamily: 'outer' },

  // Ultra Path - Counterclockwise rotation (100s) - Strategic Business Elements (12 icons, 30° spacing) - MAXIMUM SCALE
  { Icon: Calculator, color: 'text-premium-gold/50', size: 37, radius: 540, sector: 0, orbitalFamily: 'ultra' },
  { Icon: FileText, color: 'text-neon-cyan/50', size: 38, radius: 540, sector: 30, orbitalFamily: 'ultra' },
  { Icon: Handshake, color: 'text-premium-platinum/50', size: 37, radius: 540, sector: 60, orbitalFamily: 'ultra' },
  { Icon: Network, color: 'text-neon-purple/50', size: 38, radius: 540, sector: 90, orbitalFamily: 'ultra' },
  { Icon: Layers, color: 'text-neon-pink/50', size: 37, radius: 540, sector: 120, orbitalFamily: 'ultra' },
  { Icon: Package, color: 'text-premium-gold/50', size: 38, radius: 540, sector: 150, orbitalFamily: 'ultra' },
  { Icon: Workflow, color: 'text-neon-cyan/50', size: 37, radius: 540, sector: 180, orbitalFamily: 'ultra' },
  { Icon: Compass, color: 'text-premium-platinum/50', size: 38, radius: 540, sector: 210, orbitalFamily: 'ultra' },
  { Icon: Map, color: 'text-neon-purple/50', size: 37, radius: 540, sector: 240, orbitalFamily: 'ultra' },
  { Icon: Bookmark, color: 'text-neon-pink/50', size: 38, radius: 540, sector: 270, orbitalFamily: 'ultra' },
  { Icon: Headphones, color: 'text-premium-gold/50', size: 37, radius: 540, sector: 300, orbitalFamily: 'ultra' },
  { Icon: TrendingUp, color: 'text-neon-cyan/50', size: 38, radius: 540, sector: 330, orbitalFamily: 'ultra' }
];

// MAXIMUM SCALE 4-Path Orbital Configuration - Scaled for maximum visual dominance
const pricingOrbitalConfig = {
  inner: {
    baseSpeed: 40,      // Slightly slower than hero for differentiation
    direction: 1,       // Clockwise (primary direction)
    phaseOffset: 15,    // 15° offset from hero for coordination
    iconCount: 6,       // 6 icons, 60° spacing
    radius: 240         // MAXIMUM: 20% larger than enhanced (200px → 240px)
  },
  middle: {
    baseSpeed: 55,      // Balanced medium speed
    direction: -1,      // Counterclockwise (creates visual contrast)
    phaseOffset: 45,    // 45° offset for harmonic spacing
    iconCount: 8,       // 8 icons, 45° spacing
    radius: 340         // MAXIMUM: 21% larger than enhanced (280px → 340px)
  },
  outer: {
    baseSpeed: 75,      // Professional, steady rotation
    direction: 1,       // Clockwise (harmonizes with inner)
    phaseOffset: 75,    // 75° offset for coordinated positioning
    iconCount: 10,      // 10 icons, 36° spacing
    radius: 440         // MAXIMUM: 22% larger than enhanced (360px → 440px)
  },
  ultra: {
    baseSpeed: 100,     // Slow, stately background rotation
    direction: -1,      // Counterclockwise (alternating pattern)
    phaseOffset: 105,   // 105° offset for subtle depth
    iconCount: 12,      // 12 icons, 30° spacing
    radius: 540         // MAXIMUM: 23% larger than enhanced (440px → 540px)
  }
};

// Interface for orbital configuration
interface OrbitalConfig {
  orbitalFamily: string;
  sector: number;
  Icon: React.ElementType;
  color: string;
  size: number;
  radius: number;
}

// Calculate organized orbital state with staggered initial positions
const calculatePricingOrganizedState = (config: OrbitalConfig, _index: number) => {
  const { orbitalFamily, sector } = config;
  const familyConfig = pricingOrbitalConfig[orbitalFamily as keyof typeof pricingOrbitalConfig];
  
  // Calculate staggered initial rotation for continuous-running appearance
  const baseInitialRotation = (sector + familyConfig.phaseOffset) % 360;
  
  // Add time-based offset to simulate mid-cycle positioning
  const timeOffset = (Date.now() / 100) % 360;
  const initialRotation = (baseInitialRotation + timeOffset) % 360;
  
  return {
    initialRotation,
    actualSpeed: familyConfig.baseSpeed,
    direction: familyConfig.direction,
    pathRadius: familyConfig.radius
  };
};

// Smooth, coordinated orbital animation variants for pricing section
const createPricingOrbitVariants = (speed: number, initialRotation: number, direction: number) => ({
  initial: {
    rotate: initialRotation,
  },
  animate: {
    rotate: initialRotation + (360 * direction),
    transition: {
      duration: speed,
      repeat: Infinity,
      ease: "linear",
      repeatType: "loop" as const
    }
  }
});

// Enhanced floating animation variants for pricing icons - matching hero section approach
const createPricingFloatingVariants = (orbitalFamily: string, index: number) => {
  // Coordinated floating timing optimized for each path
  const floatingConfigs = {
    inner: { duration: 5.2, amplitude: 2.0, scaleRange: 1.04 },   // Energetic floating for inner path
    middle: { duration: 6.8, amplitude: 2.4, scaleRange: 1.03 },  // Dynamic floating for middle path
    outer: { duration: 8.5, amplitude: 1.8, scaleRange: 1.025 },  // Balanced floating for outer path
    ultra: { duration: 11.2, amplitude: 1.4, scaleRange: 1.015 }  // Subtle floating for ultra path
  };

  const floatingConfig = floatingConfigs[orbitalFamily as keyof typeof floatingConfigs] ||
    { duration: 7, amplitude: 2, scaleRange: 1.025 };

  // Create harmonic delay pattern based on path position
  const harmonicDelayMap = {
    inner: index * 0.35,   // Quick succession for inner path
    middle: index * 0.45,  // Medium-quick staggering for middle path
    outer: index * 0.6,    // Balanced staggering for outer path
    ultra: index * 0.8     // Gentle staggering for ultra path
  };
  const harmonicDelay = harmonicDelayMap[orbitalFamily as keyof typeof harmonicDelayMap] || index * 0.5;

  // ENHANCED: Path-specific opacity ranges for maximum visibility and dynamic depth perception
  const opacityRangeMap = {
    inner: [0.95, 1.0, 0.95],      // ENHANCED: Maximum visibility for inner path
    middle: [0.9, 0.98, 0.9],      // ENHANCED: High visibility for middle path
    outer: [0.85, 0.95, 0.85],     // ENHANCED: Strong visibility for outer path
    ultra: [0.8, 0.9, 0.8]         // ENHANCED: Good visibility for ultra path
  };
  const opacityRange = opacityRangeMap[orbitalFamily as keyof typeof opacityRangeMap] || [0.85, 0.95, 0.85];

  return {
    initial: {
      y: 0,
      scale: 1,
      opacity: opacityRange[0]
    },
    animate: {
      y: [-floatingConfig.amplitude, floatingConfig.amplitude, -floatingConfig.amplitude],
      scale: [1, floatingConfig.scaleRange, 1],
      opacity: opacityRange,
      transition: {
        duration: floatingConfig.duration,
        repeat: Infinity,
        ease: "easeInOut",
        delay: harmonicDelay
      }
    }
  };
};

interface PricingOrbitingIconsProps {
  className?: string;
}

const PricingOrbitingIcons: React.FC<PricingOrbitingIconsProps> = ({ className = '' }) => {
  // Add responsive width detection with SSR safety
  const [isSmallMobile, setIsSmallMobile] = useState(false);
  
  useEffect(() => {
    // Only run in the browser, not during SSR
    const handleResize = () => {
      setIsSmallMobile(window.innerWidth < 375);
    };
    
    // Initial check
    handleResize();
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  return (
    <div
      className={`absolute inset-0 pointer-events-none overflow-hidden pricing-orbital-container ${className}`}
      aria-hidden="true"
      role="presentation"
    >
      {/* Enhanced responsive visibility with accessibility support */}
      <div className="hidden md:block motion-safe:block motion-reduce:hidden">
        {/* Enhanced 4-Path Pricing Orbital System with coordinated animations */}
        {pricingOrbitingIcons.map((config, index) => {
          const { Icon, color, size, orbitalFamily, sector } = config;

          // Calculate enhanced orbital mechanics for this pricing icon
          const { initialRotation, actualSpeed, direction, pathRadius } =
            calculatePricingOrganizedState(config, index);

          // Enhanced z-index layering for 4-path system
          const zIndexMap = {
            inner: 4,
            middle: 3,
            outer: 2,
            ultra: 1
          };

          // ENHANCED: Path-specific opacity for maximum visibility while maintaining depth perception
          const baseOpacity = {
            inner: 1.0,     // ENHANCED: Maximum visibility for inner path
            middle: 0.95,   // ENHANCED: High visibility for middle path
            outer: 0.9,     // ENHANCED: Strong visibility for outer path
            ultra: 0.85     // ENHANCED: Good visibility for ultra path
          }[orbitalFamily] || 0.9;

          return (
            <motion.div
              key={`pricing-orbital-${orbitalFamily}-${sector}-${index}`}
              className={`orbital-icon absolute top-1/2 left-1/2 ${color}`}
              style={{
                width: pathRadius * 2,
                height: pathRadius * 2,
                marginLeft: -pathRadius,
                marginTop: -pathRadius,
                willChange: 'transform',
                transform: 'translate3d(0, 0, 0)', // Hardware acceleration
                zIndex: zIndexMap[orbitalFamily as keyof typeof zIndexMap]
              }}
              variants={createPricingOrbitVariants(actualSpeed, initialRotation, direction)}
              initial="initial"
              animate="animate"
            >
              <motion.div
                className="absolute"
                style={{
                  top: 0, // Positioned on outer edge of orbital path
                  left: '50%',
                  transform: 'translateX(-50%)',
                  willChange: 'transform'
                }}
                variants={createPricingFloatingVariants(orbitalFamily, index)}
                initial="initial"
                animate="animate"
              >
                <Icon
                  size={size}
                  strokeWidth={2.5}  // ENHANCED: Increased stroke width for visual weight
                  className="drop-shadow-lg transition-opacity duration-300 filter blur-0 hover:opacity-100"
                  style={{
                    willChange: 'opacity',
                    opacity: baseOpacity
                  }}
                />
              </motion.div>
            </motion.div>
          );
        })}
      </div>

      {/* Further mobile-optimized version with reduced complexity for better performance */}
      <div className="block md:hidden motion-safe:block motion-reduce:hidden">
        {/* Only show inner and middle paths on small mobile for better performance and reduced visual complexity */}
        {pricingOrbitingIcons.filter(config =>
          (isSmallMobile ? config.orbitalFamily === 'inner' : config.orbitalFamily === 'inner' || config.orbitalFamily === 'middle')
        ).map((config, index) => {
          const { Icon, color, size, radius, orbitalFamily, sector } = config;
          const { initialRotation, actualSpeed, direction } = calculatePricingOrganizedState(config, index);

          // Mobile-specific sizing with improved proportional scaling for better small screen display
          const mobileSize = Math.max(size * 0.65, 18); // Smaller icons for mobile
          const mobileRadius = radius * 0.5;           // Smaller orbit radius for mobile

          return (
            <motion.div
              key={`pricing-mobile-orbital-${orbitalFamily}-${sector}-${index}`}
              className={`orbital-icon absolute top-1/2 left-1/2 ${color}`}
              style={{
                width: mobileRadius * 2,
                height: mobileRadius * 2,
                marginTop: -mobileRadius,
                marginLeft: -mobileRadius,
                willChange: 'transform',
                transform: 'translate3d(0, 0, 0)' // Hardware acceleration
              }}
              variants={createPricingOrbitVariants(actualSpeed * 1.3, initialRotation, direction)} // Faster on mobile for better perceived performance
              initial="initial"
              animate="animate"
            >
              <motion.div
                className="absolute"
                style={{
                  top: 0,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  willChange: 'transform'
                }}
                variants={createPricingFloatingVariants(orbitalFamily, index)}
                initial="initial"
                animate="animate"
              >
                <Icon
                  size={mobileSize}
                  strokeWidth={2.5}  // ENHANCED: Increased stroke width for mobile visual weight
                  className="drop-shadow-lg transition-opacity duration-300"
                  style={{
                    willChange: 'opacity',
                    opacity: 0.8  // ENHANCED: Much higher mobile visibility
                  }}
                />
              </motion.div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default PricingOrbitingIcons;
