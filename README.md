# Econic Media - Luxury Web Development Company

A premium web development company website featuring modern design with glassmorphism effects, dark theme with cyan/purple neon accents, and sophisticated animations.

## 🚀 Live Demo

**Production URL**: [Coming Soon - Deploy to Vercel]

## ✨ Features

- **Luxury Design Aesthetic**: Dark theme with cyan/purple neon accents and glassmorphism effects
- **Responsive Design**: Optimized for mobile (320px-767px), tablet (768px-1023px), and desktop (1024px+)
- **Performance Optimized**: Built with Vite, lazy loading, and code splitting
- **Modern Animations**: Framer Motion animations with hover effects
- **SEO Optimized**: Meta tags, structured data, and sitemap
- **Analytics Integration**: Vercel Analytics and Speed Insights
- **Accessibility**: WCAG compliant with proper ARIA labels
- **Progressive Web App**: Service worker and offline capabilities

## 🛠️ Technology Stack

### Frontend
- **Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite 5.4.1
- **Styling**: Tailwind CSS with custom glassmorphism effects
- **UI Components**: shadcn/ui with Radix UI primitives
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **State Management**: React Context + React Query

### Development Tools
- **Linting**: ESLint with TypeScript support
- **Testing**: Jest + React Testing Library
- **Package Manager**: npm
- **Version Control**: Git

### Deployment & Analytics
- **Platform**: Vercel
- **Analytics**: Vercel Analytics
- **Performance**: Vercel Speed Insights
- **Domain**: Custom domain ready

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

### Installation

```bash
# Clone the repository
git clone <YOUR_GIT_URL>

# Navigate to project directory
cd econic-media

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts

```bash
# Development server (localhost already running in background)
npm run dev

# Production build
npm run build

# Development build
npm run build:dev

# Lint code
npm run lint

# Preview production build
npm run preview
```

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Connect to Vercel**:
   ```bash
   # Install Vercel CLI (if not already installed)
   npm install -g vercel

   # Login to Vercel
   vercel login

   # Deploy to Vercel
   vercel
   ```

2. **Environment Variables** (if needed):
   - No environment variables required for basic deployment
   - Analytics work automatically with Vercel deployment

3. **Custom Domain**:
   - Configure custom domain in Vercel dashboard
   - Update DNS records as instructed by Vercel

### Manual Deployment

```bash
# Build for production
npm run build

# The dist/ folder contains the built application
# Upload contents to your hosting provider
```

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   ├── sections/       # Page sections (Hero, About, etc.)
│   └── __tests__/      # Component tests
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── lib/                # Utility functions and configurations
├── context/            # React context providers
└── utils/              # Helper utilities
```

## 🎨 Design System

### Color Palette
- **Primary**: Cyan neon (#00ffff)
- **Secondary**: Purple neon (#8b5cf6)
- **Background**: Dark gradients
- **Glass**: Glassmorphism effects with backdrop blur

### Responsive Breakpoints
- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px+

## 🔧 Development Guidelines

### Code Style
- Follow ESLint configuration
- Use TypeScript for type safety
- Implement proper error boundaries
- Follow React best practices

### Performance
- Lazy load components where appropriate
- Optimize images and assets
- Use React.memo for expensive components
- Implement proper loading states

## 📈 Analytics & Monitoring

- **Vercel Analytics**: User behavior and page views
- **Speed Insights**: Core Web Vitals and performance metrics
- **Error Boundaries**: Graceful error handling
- **Performance Monitoring**: Custom performance hooks

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is proprietary and confidential. All rights reserved by Econic Media.

## 📞 Support

For support and inquiries, please contact the development team.
