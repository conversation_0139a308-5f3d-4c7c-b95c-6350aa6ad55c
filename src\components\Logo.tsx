import React from 'react';

type LogoVariant = 'default' | 'hero' | 'footer';

interface LogoProps {
  className?: string;
  alt?: string;
  variant?: LogoVariant;
}

const Logo: React.FC<LogoProps> = ({ 
  className = "", 
  alt = "Econic Media Logo",
  variant = "default"
}) => {
  // Using the new logo file for all variants
  const getLogoSrc = () => {
    return "/newlogofinal.png";
  };
  
  // Apply different size classes based on variant
  const sizeClasses = () => {
    switch(variant) {
      case 'hero':
        return 'h-[80px] sm:h-[100px] md:h-[120px] w-auto';
      case 'footer':
        return 'h-[60px] sm:h-[80px] md:h-[100px] w-auto';
      default:
        return 'h-[50px] sm:h-[70px] md:h-[90px] w-auto';
    }
  };
  
  // Apply different alignment classes based on variant
  const alignmentClasses = () => {
    switch(variant) {
      case 'footer':
        return 'flex items-center justify-start gap-2'; // Left-aligned for footer
      case 'hero':
        return 'flex items-center justify-center gap-2'; // Center-aligned for hero
      default:
        return 'flex items-center justify-center gap-2'; // Center-aligned for default
    }
  };

  return (
    <div className={alignmentClasses()}>
      {/* Using the appropriate logo based on variant with responsive sizing */}
      <img
        src={getLogoSrc()}
        alt={alt}
        className={`${sizeClasses()} ${className} object-contain transition-all duration-300`}
      />
    </div>
  );
};

export default Logo;
