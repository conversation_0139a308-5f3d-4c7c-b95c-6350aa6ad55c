<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .image-test {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
        }
        .image-test img {
            max-width: 200px;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
            margin: 10px;
        }
        .success {
            color: #4ade80;
        }
        .error {
            color: #ef4444;
        }
        .url-display {
            font-family: monospace;
            background: #2a2a2a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Image Loading Test</h1>
        <p>Testing if images with spaces in filenames load correctly:</p>
        
        <div class="image-test">
            <h3>Test 1: Direct URLs with spaces</h3>
            <div class="url-display">URL: /Product Pictures/1 (5).png</div>
            <img src="/Product Pictures/1 (5).png" alt="Test image 5" onload="markSuccess(this)" onerror="markError(this)">
            
            <div class="url-display">URL: /Product Pictures/1 (6).png</div>
            <img src="/Product Pictures/1 (6).png" alt="Test image 6" onload="markSuccess(this)" onerror="markError(this)">
            
            <div class="url-display">URL: /Product Pictures/1 (7).png</div>
            <img src="/Product Pictures/1 (7).png" alt="Test image 7" onload="markSuccess(this)" onerror="markError(this)">
            
            <div class="url-display">URL: /Product Pictures/1 (8).png</div>
            <img src="/Product Pictures/1 (8).png" alt="Test image 8" onload="markSuccess(this)" onerror="markError(this)">
        </div>
        
        <div class="image-test">
            <h3>Test 2: URL-encoded URLs</h3>
            <div class="url-display">URL: /Product%20Pictures/1%20(5).png</div>
            <img src="/Product%20Pictures/1%20(5).png" alt="Test image 5 encoded" onload="markSuccess(this)" onerror="markError(this)">
            
            <div class="url-display">URL: /Product%20Pictures/1%20(6).png</div>
            <img src="/Product%20Pictures/1%20(6).png" alt="Test image 6 encoded" onload="markSuccess(this)" onerror="markError(this)">
            
            <div class="url-display">URL: /Product%20Pictures/1%20(7).png</div>
            <img src="/Product%20Pictures/1%20(7).png" alt="Test image 7 encoded" onload="markSuccess(this)" onerror="markError(this)">
            
            <div class="url-display">URL: /Product%20Pictures/1%20(8).png</div>
            <img src="/Product%20Pictures/1%20(8).png" alt="Test image 8 encoded" onload="markSuccess(this)" onerror="markError(this)">
        </div>
        
        <div id="results">
            <h3>Results:</h3>
            <div id="success-count">✅ Loaded: 0</div>
            <div id="error-count">❌ Failed: 0</div>
        </div>
    </div>

    <script>
        let successCount = 0;
        let errorCount = 0;
        
        function markSuccess(img) {
            img.style.border = '3px solid #4ade80';
            successCount++;
            updateResults();
            console.log('✅ Image loaded successfully:', img.src);
        }
        
        function markError(img) {
            img.style.border = '3px solid #ef4444';
            img.style.opacity = '0.5';
            errorCount++;
            updateResults();
            console.error('❌ Image failed to load:', img.src);
        }
        
        function updateResults() {
            document.getElementById('success-count').textContent = `✅ Loaded: ${successCount}`;
            document.getElementById('error-count').textContent = `❌ Failed: ${errorCount}`;
        }
        
        // Test URL encoding function
        function testUrlEncoding() {
            const testUrls = [
                '/Product Pictures/1 (5).png',
                '/Product Pictures/1 (6).png',
                '/Product Pictures/1 (7).png',
                '/Product Pictures/1 (8).png',
            ];
            
            console.log('Testing URL encoding:');
            testUrls.forEach(url => {
                const encoded = encodeURI(url);
                console.log(`Original: ${url}`);
                console.log(`Encoded:  ${encoded}`);
                console.log('---');
            });
        }
        
        // Run tests when page loads
        window.addEventListener('load', () => {
            testUrlEncoding();
            console.log('Image loading test page loaded. Check the images above and console for results.');
        });
    </script>
</body>
</html>
