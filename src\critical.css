/* Critical CSS for above-the-fold content - loaded inline for fastest rendering */

/* Essential base styles */
html {
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: hsl(225, 30%, 6%);
  color: hsl(0, 0%, 98%);
  overflow-x: hidden;
  font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  font-optical-sizing: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Critical layout styles */
.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.relative {
  position: relative;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.z-40 {
  z-index: 40;
}

.z-30 {
  z-index: 30;
}

.z-20 {
  z-index: 20;
}

.z-10 {
  z-index: 10;
}

/* Critical spacing */
.p-4 {
  padding: 1rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Critical width/height */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.w-16 {
  width: 4rem;
}

.h-16 {
  height: 4rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-4xl {
  max-width: 56rem;
}

/* Critical colors */
.bg-background {
  background-color: hsl(225, 30%, 6%);
}

.text-foreground {
  color: hsl(0, 0%, 98%);
}

.text-white {
  color: #ffffff;
}

.border-white\/10 {
  border-color: rgba(255, 255, 255, 0.1);
}

.bg-white\/5 {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Critical backdrop blur */
.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* Critical animations - minimal for performance */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Critical border radius */
.rounded-full {
  border-radius: 9999px;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

/* Critical borders */
.border {
  border-width: 1px;
}

.border-4 {
  border-width: 4px;
}

.border-t-neon-cyan {
  border-top-color: hsl(192, 100%, 50%);
}

/* Critical text styles */
.text-center {
  text-align: center;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

/* Critical responsive utilities */
@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }
  
  .md\:hidden {
    display: none;
  }
  
  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }
  
  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }
}

@media (min-width: 1024px) {
  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }
  
  .lg\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }
}

/* Critical glass effect */
.glass-card {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease-in-out;
}

/* Performance optimization: Disable animations initially */
.no-animations * {
  animation-duration: 0s !important;
  animation-delay: 0s !important;
  transition-duration: 0s !important;
}

/* Optimized image component styles for better performance */
.optimized-image-container {
  position: relative;
  overflow: hidden;
}

.optimized-image-container[data-width] {
  width: var(--image-width, 100%);
}

.optimized-image-container[data-height] {
  height: var(--image-height, auto);
}

.optimized-image-container[data-aspect-ratio] {
  padding-bottom: var(--aspect-ratio, 0);
}

/* Image loading states */
.optimized-image {
  transition: opacity 0.3s ease-in-out;
  opacity: 0;
}

.optimized-image--loaded {
  opacity: 1;
}

.optimized-image--loading {
  opacity: 0;
}

/* Placeholder styles */
.optimized-image-placeholder {
  position: absolute;
  inset: 0;
  background-color: rgba(255, 255, 255, 0.1);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  display: flex;
  align-items: center;
  justify-content: center;
}

.optimized-image-placeholder[data-placeholder-color] {
  background-color: var(--placeholder-color, rgba(255, 255, 255, 0.1));
}

/* Error state */
.optimized-image-error {
  min-height: 200px;
  background-color: rgba(107, 114, 128, 0.5);
  color: rgba(156, 163, 175, 1);
  font-size: 0.875rem;
  padding: 1rem;
  border-radius: 0.375rem;
}

/* Critical performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}
