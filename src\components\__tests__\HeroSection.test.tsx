import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { LanguageProvider } from '@/context/LanguageContext';
import HeroSection from '../HeroSection';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
  },
}));

// Mock hooks
jest.mock('@/hooks/useParallax', () => ({
  useParallax: () => ({ ref: null, style: {} }),
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <LanguageProvider>
        {component}
      </LanguageProvider>
    </BrowserRouter>
  );
};

describe('HeroSection', () => {
  it('renders without crashing', () => {
    renderWithProviders(<HeroSection />);
  });

  it('displays the main heading', () => {
    renderWithProviders(<HeroSection />);

    // Check for key text content
    expect(screen.getByText(/Elevate Your Digital Presence/i)).toBeInTheDocument();
  });

  it('displays call-to-action buttons', () => {
    renderWithProviders(<HeroSection />);

    // Check for CTA buttons
    const buttons = screen.getAllByRole('button');
    expect(buttons.length).toBeGreaterThan(0);
  });

  it('has proper accessibility attributes', () => {
    renderWithProviders(<HeroSection />);

    // Check for proper heading structure
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toBeInTheDocument();
  });
});
