import React, { useEffect, useRef } from 'react';
import { useLanguage } from '../context/LanguageContext';
import { Smartphone } from 'lucide-react';
import { motion, useAnimation } from 'framer-motion';
import ScrollReveal from './ScrollReveal';
import {
  Palette, Share2, Rotate3d, Layout, Film, Camera,
  Code, ShoppingCart, Database, Settings, Shield, Wrench,
  BarChart3, Bot
} from 'lucide-react';

const ExpertiseSection: React.FC = () => {
  const { t } = useLanguage();

  const controls = useAnimation();
  const sectionRef = useRef<HTMLDivElement>(null);
  
  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' ?
    window.matchMedia('(prefers-reduced-motion: reduce)').matches : false;

  useEffect(() => {
    // Always ensure content is visible - prioritize functionality over animations
    controls.start('visible');
  }, [controls]);
  
  // Enhanced animation variants - ensuring content is always visible
  const sectionVariants = {
    hidden: {
      opacity: 1, // Always keep opacity at 1 to ensure content visibility
      y: 0
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: prefersReducedMotion ? 0.1 : 0.6,
        ease: 'easeOut',
        staggerChildren: prefersReducedMotion ? 0.01 : 0.15
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 1, // Always keep opacity at 1 to ensure content visibility
      y: 0,
      scale: 1
    },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: prefersReducedMotion ? 0.1 : 0.5,
        ease: 'easeOut',
        delay: prefersReducedMotion ? 0 : index * 0.1
      }
    })
  };
  


  const serviceCategories = [
    {
      titleKey: "expertise.branding.title",
      descriptionKey: "expertise.branding.description",
      icon: <Palette size={36} className="text-neon-blue" />,
    },
    {
      titleKey: "expertise.social.title",
      descriptionKey: "expertise.social.description",
      icon: <Share2 size={36} className="text-neon-pink" />,
    },
    {
      titleKey: "expertise.3d.title",
      descriptionKey: "expertise.3d.description",
      icon: <Rotate3d size={36} className="text-neon-yellow" />,
    },
    {
      titleKey: "expertise.uiux.title",
      descriptionKey: "expertise.uiux.description",
      icon: <Layout size={36} className="text-neon-purple" />,
    },
    {
      titleKey: "expertise.video.title",
      descriptionKey: "expertise.video.description",
      icon: <Film size={36} className="text-neon-red" />,
    },
    {
      titleKey: "expertise.photography.title",
      descriptionKey: "expertise.photography.description",
      icon: <Camera size={36} className="text-neon-cyan" />,
    },
    {
      titleKey: "expertise.webdev.title",
      descriptionKey: "expertise.webdev.description",
      icon: <Code size={36} className="text-neon-blue" />,
    },
    {
      titleKey: "expertise.ecommerce.title",
      descriptionKey: "expertise.ecommerce.description",
      icon: <ShoppingCart size={36} className="text-neon-green" />,
    },
    {
      titleKey: "expertise.mobile.title",
      descriptionKey: "expertise.mobile.description",
      icon: <Smartphone size={36} className="text-neon-purple" />,
    },
    {
      titleKey: "expertise.cms.title",
      descriptionKey: "expertise.cms.description",
      icon: <Database size={36} className="text-neon-yellow" />,
    },
    {
      titleKey: "expertise.ai.title",
      descriptionKey: "expertise.ai.description",
      icon: <Bot size={36} className="text-neon-pink" />,
    },
    {
      titleKey: "expertise.maintenance.title",
      descriptionKey: "expertise.maintenance.description",
      icon: <Wrench size={36} className="text-neon-cyan" />,
    },
    {
      titleKey: "expertise.dashboard.title",
      descriptionKey: "expertise.dashboard.description",
      icon: <BarChart3 size={36} className="text-neon-red" />,
    },
    {
      titleKey: "expertise.api.title",
      descriptionKey: "expertise.api.description",
      icon: <Settings size={36} className="text-neon-blue" />,
    },
    {
      titleKey: "expertise.security.title",
      descriptionKey: "expertise.security.description",
      icon: <Shield size={36} className="text-neon-green" />,
    },
  ];



  return (
    <section ref={sectionRef} id="expertise" className="section-padding relative overflow-visible">
      {/* Unified background layers for optimal readability and visual harmony */}
      <div className="absolute inset-0 bg-gradient-cosmic-dark"></div>
      <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
      <div className="absolute inset-0 bg-gradient-radial from-neon-purple/4 via-transparent to-neon-cyan/2"></div>
      <div className="absolute inset-0 bg-gradient-aurora opacity-1"></div>
      <div className="absolute inset-0 bg-black/40"></div>

      {/* Enhanced gradient orb decorations optimized for darker background - Static */}
      <div className="absolute top-10 left-10 w-48 h-48 bg-gradient-ocean opacity-15 rounded-full filter blur-3xl z-0"></div>
      <div className="absolute bottom-20 right-20 w-48 h-48 bg-gradient-sunset opacity-12 rounded-full filter blur-3xl z-0"></div>
      <div className="absolute top-1/2 left-1/2 w-96 h-96 bg-gradient-aurora opacity-8 rounded-full filter blur-3xl z-0"></div>



      <motion.div
        className="container max-w-7xl mx-auto relative z-30" /* Higher z-index to ensure content stays above decorative elements */
        initial="visible"
        animate="visible"
        variants={sectionVariants}
      >
        <div className="text-center">
          <ScrollReveal>
            <motion.h2
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold text-gradient-aurora drop-shadow-glow mb-12"
            >
              {t('expertise.title')}
            </motion.h2>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 relative z-30">
            {serviceCategories.map((category, index) => {
              const title = t(category.titleKey);
              const description = t(category.descriptionKey);

              return (
                <ScrollReveal key={index} delay={index * 100} direction="up">
                  <div className="expertise-card group cursor-pointer relative overflow-hidden rounded-2xl backdrop-blur-lg bg-white/5 shadow-[0_4px_12px_-2px_rgba(0,0,0,0.3)] border border-white/10 transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-lg z-30 opacity-100 block visible h-full flex flex-col">
                    <div className="relative z-30 p-6 sm:p-8 flex flex-col h-full">
                      <div className="flex items-center justify-center mb-6 p-4 rounded-full bg-gradient-glass group-hover:bg-gradient-aurora/20 transition-all duration-300 flex-shrink-0">
                        {category.icon}
                      </div>
                      <h3 className="text-xl sm:text-2xl font-semibold text-white mb-4 group-hover:text-gradient-aurora transition-all duration-300 flex-shrink-0 text-center">
                        {title || category.titleKey}
                      </h3>
                      <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors duration-300 flex-grow text-center text-sm sm:text-base">
                        {description || category.descriptionKey}
                      </p>
                    </div>
                  </div>
                </ScrollReveal>
              );
            })}
          </div>
        </div>
      </motion.div>
    </section>
  );
};

export default ExpertiseSection;
