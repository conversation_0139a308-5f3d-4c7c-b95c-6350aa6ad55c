import React, { useEffect } from 'react';

/**
 * ResourceHints component to improve loading performance by providing resource hints to the browser
 * This component should be included in the App component
 */
const ResourceHints: React.FC = () => {
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    try {
      // Add preconnect for external domains with proper crossorigin
      addPreconnect([
        { url: 'https://fonts.googleapis.com', crossorigin: true },
        { url: 'https://fonts.gstatic.com', crossorigin: true },
        { url: 'https://cdn.vercel-insights.com', crossorigin: false }
      ]);

      // Add DNS prefetch for less critical domains
      addDnsPrefetch([
        'https://vercel.com'
      ]);

      // Load Google Fonts CSS with proper preload
      loadGoogleFonts();

      // Note: CSS preloading removed to prevent unused preload warnings
      // Vite handles CSS loading automatically and efficiently
    } catch (error) {
      console.warn('ResourceHints: Error setting up resource hints:', error);
    }
  }, []);

  return null; // This component doesn't render anything
};

function addPreconnect(configs: Array<{ url: string; crossorigin: boolean }>) {
  configs.forEach(({ url, crossorigin }) => {
    try {
      if (!document.querySelector(`link[rel="preconnect"][href="${url}"]`)) {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = url;
        if (crossorigin) {
          link.crossOrigin = 'anonymous';
        }
        document.head.appendChild(link);
      }
    } catch (error) {
      console.warn(`Failed to add preconnect for ${url}:`, error);
    }
  });
}

function addDnsPrefetch(urls: string[]) {
  urls.forEach(url => {
    try {
      if (!document.querySelector(`link[rel="dns-prefetch"][href="${url}"]`)) {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = url;
        document.head.appendChild(link);
      }
    } catch (error) {
      console.warn(`Failed to add DNS prefetch for ${url}:`, error);
    }
  });
}

function loadGoogleFonts() {
  try {
    // Check if Google Fonts CSS is already loaded
    if (!document.querySelector('link[href*="fonts.googleapis.com"]')) {
      // Preload font files for critical fonts first
      const interPreload = document.createElement('link');
      interPreload.rel = 'preload';
      interPreload.as = 'font';
      interPreload.type = 'font/woff2';
      interPreload.href = 'https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2';
      interPreload.crossOrigin = 'anonymous';
      document.head.appendChild(interPreload);

      // Load CSS with optimized font weights (only essential weights)
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      // Reduced font weights for better performance - only load essential weights
      link.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap';
      link.crossOrigin = 'anonymous';

      // Optimize loading with media attribute
      link.media = 'print';
      link.onload = () => {
        link.media = 'all';
        // Add CSS to ensure font-display: swap is applied
        const style = document.createElement('style');
        style.textContent = `
          @font-face {
            font-family: 'Inter';
            font-display: swap;
          }
          @font-face {
            font-family: 'Poppins';
            font-display: swap;
          }
        `;
        document.head.appendChild(style);
      };

      document.head.appendChild(link);
    }
  } catch (error) {
    console.warn('Failed to load Google Fonts:', error);
  }
}

export default ResourceHints;
