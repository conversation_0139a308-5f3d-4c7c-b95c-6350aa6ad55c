<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logo Creator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #f5f5f5;
        }
        h1, h2 {
            color: #f5f5f5;
        }
        .canvas-container {
            margin: 20px 0;
            border: 1px solid #444;
            padding: 20px;
            background-color: #222;
            border-radius: 5px;
        }
        canvas {
            border: 1px solid #444;
            margin-bottom: 20px;
            background-color: transparent;
        }
        button {
            background-color: #3a86ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #2a66df;
        }
        .instructions {
            background-color: #333;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .download-links {
            margin-top: 20px;
        }
        a {
            color: #3a86ff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>Test Logo Creator</h1>
    <p>This tool creates temporary test versions of the logo files needed for the website.</p>
    
    <div class="instructions">
        <h2>Instructions:</h2>
        <p>1. Click the "Generate Test Logos" button below to create simple placeholder logos</p>
        <p>2. Click the download links to save each image</p>
        <p>3. Place these files in the /public folder of your project</p>
        <p>4. These are just placeholders until you can create proper versions from the original logo</p>
    </div>

    <button id="generateButton">Generate Test Logos</button>
    
    <div class="canvas-container">
        <h2>E Icon (e-icon.png)</h2>
        <canvas id="iconCanvas" width="200" height="200"></canvas>
        <div class="download-links">
            <a id="iconDownload" download="e-icon.png" href="#">Download E Icon</a>
        </div>
    </div>
    
    <div class="canvas-container">
        <h2>Text Logo (econic-text.png)</h2>
        <canvas id="textCanvas" width="400" height="100"></canvas>
        <div class="download-links">
            <a id="textDownload" download="econic-text.png" href="#">Download Text Logo</a>
        </div>
    </div>
    
    <div class="canvas-container">
        <h2>Full Logo (logo.png)</h2>
        <canvas id="fullCanvas" width="600" height="200"></canvas>
        <div class="download-links">
            <a id="fullDownload" download="logo.png" href="#">Download Full Logo</a>
        </div>
    </div>

    <script>
        // Add roundRect polyfill for browsers that don't support it
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                if (width < 2 * radius) radius = width / 2;
                if (height < 2 * radius) radius = height / 2;
                this.beginPath();
                this.moveTo(x + radius, y);
                this.arcTo(x + width, y, x + width, y + height, radius);
                this.arcTo(x + width, y + height, x, y + height, radius);
                this.arcTo(x, y + height, x, y, radius);
                this.arcTo(x, y, x + width, y, radius);
                this.closePath();
                return this;
            };
        }

        document.getElementById('generateButton').addEventListener('click', function() {
            // Generate E Icon
            const iconCanvas = document.getElementById('iconCanvas');
            const iconCtx = iconCanvas.getContext('2d');
            
            // Clear canvas
            iconCtx.clearRect(0, 0, iconCanvas.width, iconCanvas.height);
            
            // Draw gradient background
            const iconGradient = iconCtx.createLinearGradient(0, 0, 200, 200);
            iconGradient.addColorStop(0, '#6366f1');
            iconGradient.addColorStop(1, '#2dd4bf');
            iconCtx.fillStyle = iconGradient;
            iconCtx.roundRect(20, 20, 160, 160, 40);
            iconCtx.fill();
            
            // Draw E letter
            iconCtx.fillStyle = 'white';
            iconCtx.font = 'bold 120px Arial';
            iconCtx.fillText('E', 55, 150);
            
            // Update download link
            document.getElementById('iconDownload').href = iconCanvas.toDataURL('image/png');
            
            // Generate Text Logo
            const textCanvas = document.getElementById('textCanvas');
            const textCtx = textCanvas.getContext('2d');
            
            // Clear canvas
            textCtx.clearRect(0, 0, textCanvas.width, textCanvas.height);
            
            // Draw outline text
            textCtx.strokeStyle = 'white';
            textCtx.lineWidth = 1;
            textCtx.font = '50px Arial';
            textCtx.strokeText('Econic Media', 20, 60);
            
            // Update download link
            document.getElementById('textDownload').href = textCanvas.toDataURL('image/png');
            
            // Generate Full Logo
            const fullCanvas = document.getElementById('fullCanvas');
            const fullCtx = fullCanvas.getContext('2d');
            
            // Clear canvas
            fullCtx.clearRect(0, 0, fullCanvas.width, fullCanvas.height);
            
            // Draw icon part
            const fullGradient = fullCtx.createLinearGradient(0, 0, 160, 160);
            fullGradient.addColorStop(0, '#6366f1');
            fullGradient.addColorStop(1, '#2dd4bf');
            fullCtx.fillStyle = fullGradient;
            fullCtx.roundRect(20, 20, 160, 160, 40);
            fullCtx.fill();
            
            // Draw E letter
            fullCtx.fillStyle = 'white';
            fullCtx.font = 'bold 120px Arial';
            fullCtx.fillText('E', 55, 150);
            
            // Draw text part
            fullCtx.strokeStyle = 'white';
            fullCtx.lineWidth = 1;
            fullCtx.font = '50px Arial';
            fullCtx.strokeText('Econic Media', 200, 110);
            
            // Update download link
            document.getElementById('fullDownload').href = fullCanvas.toDataURL('image/png');
        });
    </script>
</body>
</html> 