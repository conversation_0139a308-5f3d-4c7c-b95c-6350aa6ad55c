<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Loading Test - Econic Media</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-item {
            border: 2px solid #333;
            border-radius: 8px;
            padding: 10px;
            background: #2a2a2a;
        }
        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
        }
        .image-item.success {
            border-color: #4ade80;
        }
        .image-item.error {
            border-color: #ef4444;
        }
        .status {
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
        }
        .success .status {
            color: #4ade80;
        }
        .error .status {
            color: #ef4444;
        }
        .summary {
            background: #333;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .section-title {
            color: #00d4ff;
            margin: 30px 0 15px 0;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🖼️ Image Loading Test - Econic Media</h1>
        <div class="summary">
            <h3>Test Results:</h3>
            <div id="results">
                <div>✅ Loaded: <span id="success-count">0</span></div>
                <div>❌ Failed: <span id="error-count">0</span></div>
                <div>📊 Total: <span id="total-count">0</span></div>
            </div>
        </div>

        <h2 class="section-title">Product Pictures Test</h2>
        <div id="product-images" class="image-grid"></div>

        <h2 class="section-title">Website Portfolio Test</h2>
        <div id="website-images" class="image-grid"></div>
    </div>

    <script>
        let successCount = 0;
        let errorCount = 0;
        let totalCount = 0;

        // Product images array
        const productImages = [
            '/Product Pictures/1 (1).png',
            '/Product Pictures/1 (2).png',
            '/Product Pictures/1 (3).png',
            '/Product Pictures/1 (4).png',
            '/Product Pictures/1 (5).png',
            '/Product Pictures/1 (6).png',
            '/Product Pictures/1 (7).png',
            '/Product Pictures/1 (8).png',
            '/Product Pictures/1 (9).png',
            '/Product Pictures/1 (10).png',
            '/Product Pictures/1 (11).png',
            '/Product Pictures/1 (12).png',
            '/Product Pictures/1 (13).png',
            '/Product Pictures/1 (14).png',
            '/Product Pictures/1 (15).png',
            '/Product Pictures/1 (16).png',
            '/Product Pictures/1 (17).png',
            '/Product Pictures/1 (18).png',
            '/Product Pictures/1 (19).png',
            '/Product Pictures/1 (20).png',
            '/Product Pictures/1 (21).png',
            '/Product Pictures/1 (22).png',
            '/Product Pictures/1 (23).png',
            '/Product Pictures/1 (24).png'
        ];

        // Website images array
        const websiteImages = [
            '/Websites/rein-glanz-service.png',
            '/Websites/benfresh.png',
            '/Websites/aurora-dental-clinic-min.png',
            '/Websites/securitas-security-min.png',
            '/Websites/hogan-lovells-germany-min.png',
            '/Websites/cleanwhale-berlin-min.png',
            '/Websites/superlist-productivity-min.png',
            '/Websites/linear-dev-tools-min.png',
            '/Websites/pitch-presentation-min.png'
        ];

        function createImageTest(src, container, title) {
            const item = document.createElement('div');
            item.className = 'image-item';
            
            const img = document.createElement('img');
            img.src = src;
            img.alt = title;
            
            const status = document.createElement('div');
            status.className = 'status';
            status.textContent = 'Loading...';
            
            const filename = document.createElement('div');
            filename.style.fontSize = '11px';
            filename.style.color = '#888';
            filename.textContent = src.split('/').pop();
            
            item.appendChild(img);
            item.appendChild(filename);
            item.appendChild(status);
            container.appendChild(item);
            
            totalCount++;
            updateResults();
            
            img.onload = function() {
                item.className = 'image-item success';
                status.textContent = '✅ Loaded';
                successCount++;
                updateResults();
                console.log('✅ Loaded:', src);
            };
            
            img.onerror = function() {
                item.className = 'image-item error';
                status.textContent = '❌ Failed';
                errorCount++;
                updateResults();
                console.error('❌ Failed:', src);
            };
        }

        function updateResults() {
            document.getElementById('success-count').textContent = successCount;
            document.getElementById('error-count').textContent = errorCount;
            document.getElementById('total-count').textContent = totalCount;
        }

        // Initialize tests
        function runTests() {
            const productContainer = document.getElementById('product-images');
            const websiteContainer = document.getElementById('website-images');
            
            // Test product images
            productImages.forEach((src, index) => {
                createImageTest(src, productContainer, `Product ${index + 1}`);
            });
            
            // Test website images
            websiteImages.forEach((src, index) => {
                createImageTest(src, websiteContainer, `Website ${index + 1}`);
            });
        }

        // Run tests when page loads
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
